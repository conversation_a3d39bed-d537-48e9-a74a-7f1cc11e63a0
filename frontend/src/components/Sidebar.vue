<template>
  <div class="sidebar" :class="{
    collapsed,
    'is-navigating': isNavigating,
    'sidebar-toggling': isToggling
  }">
    <!-- 侧边栏头部 -->
    <div class="sidebar-header">
      <div class="logo">
        <div class="logo-icon">
          <el-icon><Files /></el-icon>
        </div>
        <transition name="fade">
          <span v-show="!collapsed" class="logo-text">文件管理工具</span>
        </transition>
      </div>
    </div>

    <!-- 导航菜单 -->
    <nav class="sidebar-nav" role="navigation" aria-label="主导航">
      <div class="nav-section">
        <div class="section-title" v-if="!collapsed">主要功能</div>
        
        <div class="nav-items">
          <router-link
            v-for="item in mainMenuItems"
            :key="item.path"
            :to="item.path"
            class="nav-item"
            :class="{
              active: localActiveItem === item.path,
              'no-transition': isNavigating
            }"
            @click="(e) => handleNavClick(e, item.path)"
            :aria-label="item.title"
            :title="collapsed ? item.title : ''"
          >
            <div class="nav-icon">
              <el-icon><component :is="item.icon" /></el-icon>
            </div>
            <transition name="fade">
              <span v-show="!collapsed" class="nav-text">{{ item.title }}</span>
            </transition>
            <transition name="fade">
              <div v-show="!collapsed && item.badge" class="nav-badge">
                {{ item.badge }}
              </div>
            </transition>
          </router-link>
        </div>
      </div>

      <div class="nav-section">
        <div class="section-title" v-if="!collapsed">系统管理</div>
        
        <div class="nav-items">
          <router-link
            v-for="item in systemMenuItems"
            :key="item.path"
            :to="item.path"
            class="nav-item"
            :class="{
              active: localActiveItem === item.path,
              'no-transition': isNavigating
            }"
            @click="(e: Event) => handleNavClick(e, item.path)"
            :aria-label="item.title"
            :title="collapsed ? item.title : ''"
          >
            <div class="nav-icon">
              <el-icon><component :is="item.icon" /></el-icon>
            </div>
            <transition name="fade">
              <span v-show="!collapsed" class="nav-text">{{ item.title }}</span>
            </transition>
          </router-link>
        </div>
      </div>
    </nav>

    <!-- 侧边栏底部 -->
    <div class="sidebar-footer">
      <!-- 展开状态的按钮 -->
      <div
        v-if="!collapsed"
        class="collapse-btn-expanded"
        @click="handleToggle"
        role="button"
        aria-label="收起侧边栏"
        tabindex="0"
        @keydown.enter="handleToggle"
      >
        <el-icon>
          <Fold />
        </el-icon>
      </div>
      <!-- 收缩状态的按钮 -->
      <div
        v-else
        class="collapse-btn-collapsed"
        @click="handleToggle"
        role="button"
        aria-label="展开侧边栏"
        tabindex="0"
        @keydown.enter="handleToggle"
      >
        <el-icon>
          <Expand />
        </el-icon>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  Files,
  DataBoard,
  Upload,
  Edit,
  List,
  Setting,
  Expand,
  Fold
} from '@element-plus/icons-vue'

interface Props {
  collapsed: boolean
}

defineProps<Props>()
const emit = defineEmits<{
  toggle: []
}>()

const route = useRoute()
const router = useRouter()

// 简化的状态管理
const isToggling = ref(false)
const isNavigating = ref(false)
// 添加本地active状态管理，解决路由切换延迟问题
const localActiveItem = ref(route.path)

const handleToggle = () => {
  // 移除防抖逻辑，提高响应性
  emit('toggle')

  // 简化状态管理，减少延迟
  isToggling.value = true
  setTimeout(() => {
    isToggling.value = false
  }, 150) // 减少延迟时间
}

// 🎯 添加导航点击处理，提供即时反馈和状态切换
const handleNavClick = (event: Event, itemPath: string) => {
  // 🚀 立即切换本地active状态，确保无延迟
  localActiveItem.value = itemPath

  // 🌊 添加波纹效果
  const target = event.currentTarget as HTMLElement
  const rect = target.getBoundingClientRect()
  const ripple = document.createElement('div')
  const size = Math.max(rect.width, rect.height)
  const x = (event as MouseEvent).clientX - rect.left - size / 2
  const y = (event as MouseEvent).clientY - rect.top - size / 2

  ripple.style.cssText = `
    position: absolute;
    width: ${size}px;
    height: ${size}px;
    left: ${x}px;
    top: ${y}px;
    background: rgba(99, 102, 241, 0.3);
    border-radius: 50%;
    pointer-events: none;
    animation: ripple 0.4s ease-out;
    z-index: 0;
  `

  target.style.position = 'relative'
  target.appendChild(ripple)

  // 🚀 添加即时视觉反馈 - 缩短时间
  target.style.transform = 'scale(0.96)'
  target.style.transition = 'transform 0.08s ease'

  // ⚡ 移除导航状态设置，避免阻塞状态更新
  // isNavigating.value = true

  setTimeout(() => {
    target.style.transform = ''
    ripple.remove()
  }, 100)

  // ⚡ 移除导航状态恢复，让状态立即生效
  // setTimeout(() => {
  //   isNavigating.value = false
  // }, 300)
}

// ⚡ 优化路由切换处理 - 立即同步状态
let navigationTimer: NodeJS.Timeout | null = null
watch(() => route.path, (newPath) => {
  // 🚀 立即同步本地active状态和路由状态
  localActiveItem.value = newPath

  // 🔄 强制重置所有导航项状态，避免残留
  nextTick(() => {
    const allNavItems = document.querySelectorAll('.nav-item')
    allNavItems.forEach(item => {
      const element = item as HTMLElement
      // 清除可能的内联样式残留
      element.style.transform = ''
      element.style.transition = ''
      element.style.background = ''
    })
  })

  // 清除之前的定时器
  if (navigationTimer) {
    clearTimeout(navigationTimer)
  }
}, { immediate: true })

// 组件卸载时清理定时器
import { onUnmounted } from 'vue'
onUnmounted(() => {
  if (navigationTimer) {
    clearTimeout(navigationTimer)
  }
})

// 主要功能菜单项
const mainMenuItems = computed(() => [
  {
    path: '/dashboard',
    title: '仪表板',
    icon: 'DataBoard',
    badge: null
  },
  {
    path: '/files',
    title: '文件管理',
    icon: 'Upload',
    badge: null
  },
  {
    path: '/rename',
    title: '批量重命名',
    icon: 'Edit',
    badge: null
  }
])

// 系统管理菜单项
const systemMenuItems = computed(() => [
  {
    path: '/logs',
    title: '操作日志',
    icon: 'List',
    badge: null
  },
  {
    path: '/profile',
    title: '个人设置',
    icon: 'Setting',
    badge: null
  }
])

</script>

<style scoped>
/* 🎨 现代化动画关键帧 */
@keyframes smoothPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.15);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 4px 16px rgba(99, 102, 241, 0.25);
  }
}

@keyframes slideInFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideOutToLeft {
  0% {
    opacity: 1;
    transform: translateX(0);
  }
  100% {
    opacity: 0;
    transform: translateX(-20px);
  }
}

@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 0.6;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

@keyframes sidebarSlideIn {
  0% {
    opacity: 0;
    transform: translateX(-100%);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes navItemStagger {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.sidebar {
  width: 260px;
  background: var(--bg-card);
  border-right: 1px solid var(--border-primary);
  display: flex;
  flex-direction: column;
  /* 🚀 丝滑的缓动函数 */
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  min-width: 260px;
  /* 优化渲染性能 */
  contain: layout style;
  /* 添加微妙的渐变背景 */
  background: linear-gradient(135deg, var(--bg-card) 0%, rgba(255, 255, 255, 0.02) 100%);
  /* 🎬 进入动画 */
  animation: sidebarSlideIn 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.sidebar.collapsed {
  width: 64px;
  min-width: 64px;
}

/* 确保收缩状态下的内容与顶部汉堡菜单按钮对齐 */
.sidebar.collapsed .sidebar-header {
  padding: var(--space-4) 16px;
}

/* 侧边栏头部 */
.sidebar-header {
  padding: var(--space-6) var(--space-4);
  border-bottom: 1px solid var(--border-primary);
  /* 统一过渡时间 */
  transition: padding 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  overflow: hidden;
}

.sidebar.collapsed .sidebar-header {
  padding: var(--space-4) 16px;
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  white-space: nowrap;
  overflow: hidden;
}

.logo-icon {
  width: 32px;
  height: 32px;
  background: var(--primary);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--text-lg);
  flex-shrink: 0;
  transition: transform 0.2s ease;
}

.logo-icon:hover {
  transform: scale(1.05);
}

.logo-text {
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--text-primary);
  white-space: nowrap;
}

/* 导航菜单 */
.sidebar-nav {
  flex: 1;
  padding: var(--space-4) 0;
  overflow-y: auto;
  overflow-x: hidden;
}

/* 收缩状态下的导航区域对齐 */
.sidebar.collapsed .sidebar-nav {
  padding: var(--space-2) 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
}

.nav-section {
  margin-bottom: var(--space-6);
  overflow: hidden;
}

.section-title {
  font-size: var(--text-xs);
  font-weight: 600;
  color: var(--text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 0 var(--space-4);
  margin-bottom: var(--space-3);
  white-space: nowrap;
  overflow: hidden;
  text-align: left;
  /* 统一过渡时间为0.2s */
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
}


.sidebar.collapsed .nav-section {
  margin-bottom: 0;
  display: contents;
}

.sidebar.collapsed .nav-items {
  display: contents;
}

.nav-items {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
  padding: 0 var(--space-2);
  overflow: hidden;
}

/* 🎬 为导航项添加错开的进入动画 */
.nav-item:nth-child(1) { animation: navItemStagger 0.6s ease-out 0.1s both; }
.nav-item:nth-child(2) { animation: navItemStagger 0.6s ease-out 0.2s both; }
.nav-item:nth-child(3) { animation: navItemStagger 0.6s ease-out 0.3s both; }
.nav-item:nth-child(4) { animation: navItemStagger 0.6s ease-out 0.4s both; }
.nav-item:nth-child(5) { animation: navItemStagger 0.6s ease-out 0.5s both; }

.nav-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  text-decoration: none;
  /* ⚡ 快速响应的过渡 */
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  min-height: 44px;
  overflow: hidden;
  white-space: nowrap;
  cursor: pointer;
  /* 添加微妙的初始状态 */
  transform: translateX(0);
  opacity: 1;
  /* 为点击效果准备 */
  will-change: transform, background-color, box-shadow;
}

/* 简化按压效果 */
.nav-item:active {
  background: var(--bg-tertiary);
  transition: background-color 0.05s ease;
}

/* 导航时的动画控制 - 更精确的控制 */
.nav-item.no-transition {
  transition: none !important;
}

.nav-item.no-transition * {
  transition: none !important;
  animation: none !important;
}

/* 只在路由切换且非展开/收起时禁用动画 */
.sidebar.is-navigating:not(.sidebar-toggling) .nav-item {
  transition: background-color 0.1s ease, color 0.1s ease !important;
}

.sidebar.is-navigating:not(.sidebar-toggling) .nav-icon {
  transition: transform 0.1s ease !important;
}

/* 收缩状态下的普通菜单项对齐 */
.sidebar.collapsed .nav-item {
  justify-content: center;
  padding: 0;
  margin: 0 0 6px 0;
  width: 40px;
  height: 40px;
  border-radius: var(--radius-lg);
  /* ⚡ 优化过渡效果 */
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  flex-shrink: 0;
  position: relative;
  opacity: 1 !important;
  visibility: visible !important;
  /* 🎯 统一初始缩放 */
  transform: scale(1);
}

/* 收缩状态下的按压效果 */
.sidebar.collapsed .nav-item:active:not(.active) {
  transform: scale(0.95);
}

/* 激活状态的按压效果 */
.sidebar.collapsed .nav-item.active:active {
  transform: scale(0.98) !important;
}

/* 收缩状态下强制隐藏文字 */
.sidebar.collapsed .nav-text {
  display: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
}

.nav-item:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
  /* 🌟 添加微妙的悬停效果 */
  transform: translateX(4px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.nav-item:hover .nav-icon {
  /* ✨ 图标微动画 */
  transform: scale(1.1) rotate(2deg);
  color: var(--primary);
}

.nav-item:hover .nav-text {
  /* 📝 文字微妙强调 */
  font-weight: 600;
  letter-spacing: 0.01em;
}

/* 🎯 收缩状态下的悬停效果 - 修复缩放冲突 */
.sidebar.collapsed .nav-item:hover:not(.active) {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.15) 0%, rgba(99, 102, 241, 0.08) 100%);
  transform: scale(1.05) translateY(-1px);
  box-shadow: 0 4px 16px rgba(99, 102, 241, 0.2);
  border-radius: 12px;
}

.sidebar.collapsed .nav-item:hover:not(.active) .nav-icon {
  /* ✨ 图标悬停动画 - 减少旋转避免冲突 */
  transform: scale(1.1) rotate(2deg);
  color: var(--primary);
}

/* 🎯 展开状态下的active样式 - 现代化设计 */
.sidebar:not(.collapsed) .nav-item.active {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(99, 102, 241, 0.05) 100%) !important;
  color: var(--primary) !important;
  font-weight: 600 !important;
  /* ⚡ 快速状态切换 - 减少延迟 */
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow:
    inset 0 1px 3px rgba(99, 102, 241, 0.1),
    0 2px 12px rgba(99, 102, 241, 0.08) !important;
  transform: translateX(6px) !important;
  /* 添加微妙的边框 */
  border: 1px solid rgba(99, 102, 241, 0.1);
}

.sidebar:not(.collapsed) .nav-item.active::before {
  content: '';
  position: absolute;
  left: -1px;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 24px;
  background: linear-gradient(135deg, var(--primary) 0%, rgba(99, 102, 241, 0.8) 100%);
  border-radius: 0 6px 6px 0;
  /* ⚡ 快速指示器切换 */
  opacity: 1;
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

/* 非激活状态时隐藏伪元素 */
.sidebar:not(.collapsed) .nav-item:not(.active)::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 20px;
  background: var(--primary);
  border-radius: 0 2px 2px 0;
  opacity: 0;
  transition: opacity 0.1s ease;
}

/* 🌟 收缩状态下的active样式 - 修复缩放问题 */
.sidebar.collapsed .nav-item.active {
  background: linear-gradient(135deg, var(--primary) 0%, rgba(99, 102, 241, 0.9) 100%) !important;
  color: white !important;
  box-shadow:
    0 4px 20px rgba(99, 102, 241, 0.4),
    0 2px 8px rgba(99, 102, 241, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
  width: 40px !important;
  height: 40px !important;
  border-radius: 12px !important;
  justify-content: center !important;
  padding: 0 !important;
  margin: 0 0 6px 0 !important;
  display: flex !important;
  align-items: center !important;
  position: relative !important;
  /* ⚡ 快速状态切换 */
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1) !important;
  /* 🎯 合适的激活状态缩放 */
  transform: scale(1.02) !important;
}

.sidebar.collapsed .nav-item.active::before {
  display: none;
}

.sidebar.collapsed .nav-item.active:hover {
  background: linear-gradient(135deg, var(--primary-hover) 0%, var(--primary) 100%) !important;
  /* 🎯 修复缩放 - 避免过度缩放 */
  transform: translateY(-2px) scale(1.08) !important;
  box-shadow:
    0 6px 24px rgba(99, 102, 241, 0.5),
    0 3px 12px rgba(99, 102, 241, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
  /* 🎨 移除脉冲效果避免冲突 */
}

.nav-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-lg);
  flex-shrink: 0;
  /* 🎯 流畅的图标动画 */
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  /* 防止图标动画残影 */
  will-change: transform, color;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  /* 添加微妙的初始状态 */
  transform-origin: center;
}

/* 收缩状态下的图标优化 */
.sidebar.collapsed .nav-icon {
  width: 20px;
  height: 20px;
  font-size: var(--text-lg);
  opacity: 1 !important;
  visibility: visible !important;
}

.nav-text {
  flex: 1;
  font-size: var(--text-sm);
  font-weight: 500;
  white-space: nowrap;
  overflow: visible;
  text-overflow: clip;
  min-width: 0;
  /* 🎯 文字流畅过渡 */
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform-origin: left center;
}

.nav-badge {
  background: var(--danger);
  color: white;
  font-size: var(--text-xs);
  padding: 2px 6px;
  border-radius: var(--radius-full);
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 侧边栏底部 */
.sidebar-footer {
  padding: var(--space-4);
  border-top: 1px solid var(--border-primary);
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  height: 72px;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 🎯 展开状态的按钮 - 现代化设计 */
.collapse-btn-expanded {
  position: absolute;
  width: calc(100% - var(--space-4) * 2);
  height: 40px;
  background: linear-gradient(135deg, var(--bg-hover) 0%, rgba(255, 255, 255, 0.02) 100%);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  opacity: 1;
  transform: scale(1);
  /* 添加微妙的阴影 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.collapse-btn-expanded:active {
  transform: scale(0.95);
}

/* 🌟 收缩状态的按钮 - 现代化设计 */
.collapse-btn-collapsed {
  position: absolute;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary) 0%, rgba(99, 102, 241, 0.9) 100%);
  color: white;
  border: none;
  box-shadow:
    0 4px 16px rgba(99, 102, 241, 0.4),
    0 2px 8px rgba(99, 102, 241, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  opacity: 0;
  transform: scale(0.8) rotate(-180deg);
}

.collapse-btn-collapsed:active {
  transform: scale(0.9) rotate(-180deg);
}


/* 收缩状态 */
.sidebar.collapsed .sidebar-footer {
  padding: var(--space-2);
  border-top: none;
  height: 52px;
}

.sidebar.collapsed .collapse-btn-expanded {
  opacity: 0;
  transform: scale(0.8);
}

.sidebar.collapsed .collapse-btn-collapsed {
  opacity: 1;
  transform: scale(1) rotate(0deg);
}

/* 🎨 悬停效果 - 现代化 */
.collapse-btn-expanded:hover {
  background: linear-gradient(135deg, var(--bg-tertiary) 0%, rgba(99, 102, 241, 0.05) 100%);
  color: var(--text-primary);
  border-color: var(--primary);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.collapse-btn-collapsed:hover {
  background: linear-gradient(135deg, var(--primary-hover) 0%, var(--primary) 100%);
  transform: scale(1.1) translateY(-2px);
  box-shadow:
    0 6px 24px rgba(99, 102, 241, 0.5),
    0 3px 12px rgba(99, 102, 241, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* 🎯 现代化fade动画 */
.fade-enter-active {
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  animation: slideInFromLeft 0.4s ease-out;
}

.fade-leave-active {
  transition: all 0.3s cubic-bezier(0.55, 0.085, 0.68, 0.53);
  animation: slideOutToLeft 0.3s ease-in;
}

.fade-enter-from {
  opacity: 0;
  transform: translateX(-20px);
}

.fade-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}


/* 滚动条样式 */
.sidebar-nav::-webkit-scrollbar {
  width: 4px;
}

.sidebar-nav::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-nav::-webkit-scrollbar-thumb {
  background: var(--border-primary);
  border-radius: 2px;
}

.sidebar-nav::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    left: 0;
    top: 0;
    height: 100vh;
    z-index: 1001;
  }
  
  .sidebar.collapsed {
    transform: translateX(-100%);
  }
  
  /* 移动端优化 */
  .nav-item {
    min-height: 48px;
  }
}

/* 全局优化：减少重绘和重排 */
.sidebar * {
  /* 避免不必要的重绘 */
  contain: layout style paint;
}

/* 导航项容器优化 */
.nav-items {
  /* 创建新的层叠上下文，隔离动画影响 */
  isolation: isolate;
}

/* 清理动画残留 */
.nav-item::before,
.nav-item::after {
  /* 确保伪元素在非激活状态下完全透明 */
  pointer-events: none;
}

/* 修复快速切换时的闪烁 */
.nav-item {
  /* 防止内容溢出导致的闪烁 */
  overflow: hidden;
  /* 确保文字不会导致布局偏移 */
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 优化导航时的动画处理 - 避免过度禁用 */
.sidebar.is-navigating:not(.sidebar-toggling) .fade-enter-active,
.sidebar.is-navigating:not(.sidebar-toggling) .fade-leave-active {
  /* 加快fade动画，减少视觉延迟 */
  transition-duration: 0.1s !important;
}

/* 侧边栏切换时的优化 */
.sidebar.sidebar-toggling {
  /* 确保切换动画优先级最高 */
  z-index: 1001;
}

.sidebar.sidebar-toggling .fade-enter-active,
.sidebar.sidebar-toggling .fade-leave-active {
  /* 切换时保持正常的fade动画时间 */
  transition: opacity 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

/* 优化收缩/展开动画 */
.sidebar,
.sidebar * {
  /* 防止动画期间的闪烁 */
  -webkit-tap-highlight-color: transparent;
}

/* 确保激活状态切换时的平滑过渡 */
.nav-item {
  /* 使用 transform 而不是 position 来避免重排 */
  position: relative;
  z-index: 1;
}

.nav-item.active {
  /* 提升激活项的层级，避免与其他元素冲突 */
  z-index: 2;
}

/* 修复伪元素可能的残留 */
.nav-item::before {
  /* 确保伪元素始终在正确的位置 */
  pointer-events: none;
  will-change: opacity;
}

/* ⚡ 确保状态快速切换 */
.nav-item:not(.active) {
  /* 快速清除非active状态 */
  background: transparent;
  border: none;
  box-shadow: none;
}

/* 展开状态下的非激活项 */
.sidebar:not(.collapsed) .nav-item:not(.active) {
  transform: translateX(0);
}

/* 收缩状态下的非激活项 */
.sidebar.collapsed .nav-item:not(.active) {
  transform: scale(1);
}

.nav-item:not(.active)::before {
  /* 立即隐藏非active项的伪元素 */
  opacity: 0;
}

/* 优化移动端性能 */
@media (hover: none) and (pointer: coarse) {
  .nav-item {
    /* 移动端禁用 hover 动画 */
    transition: background-color 0.15s ease, color 0.15s ease;
  }
  
  .nav-item:hover {
    transform: none;
  }
}
</style>
